import ccxt
import pandas as pd
import numpy as np
import asyncio
import websocket
import json
import threading
import time
from datetime import datetime, timedelta
from config import Config
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CryptoDataCollector:
    def __init__(self):
        self.config = Config()
        self.exchanges = self._initialize_exchanges()
        self.data_cache = {}
        self.websocket_connections = {}
        self.is_running = False
        
    def _initialize_exchanges(self):
        """Initialize cryptocurrency exchanges"""
        exchanges = {}
        
        # Binance
        if self.config.BINANCE_API_KEY:
            exchanges['binance'] = ccxt.binance({
                'apiKey': self.config.BINANCE_API_KEY,
                'secret': self.config.BINANCE_SECRET_KEY,
                'sandbox': False,
                'enableRateLimit': True,
            })
        
        # Add more exchanges as needed
        exchanges['binance_public'] = ccxt.binance({
            'enableRateLimit': True,
        })
        
        return exchanges
    
    def get_historical_data(self, symbol, timeframe='1h', limit=1000):
        """Fetch historical OHLCV data"""
        try:
            exchange = self.exchanges['binance_public']
            ohlcv = exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            return df
        except Exception as e:
            logger.error(f"Error fetching historical data for {symbol}: {e}")
            return None
    
    def get_real_time_data(self, symbol):
        """Get real-time ticker data"""
        try:
            exchange = self.exchanges['binance_public']
            ticker = exchange.fetch_ticker(symbol)
            return ticker
        except Exception as e:
            logger.error(f"Error fetching real-time data for {symbol}: {e}")
            return None
    
    def start_websocket_stream(self, symbols):
        """Start WebSocket stream for real-time data"""
        def on_message(ws, message):
            try:
                data = json.loads(message)
                symbol = data.get('s', '').replace('USDT', '/USDT')
                
                if symbol in symbols:
                    self.data_cache[symbol] = {
                        'price': float(data.get('c', 0)),
                        'volume': float(data.get('v', 0)),
                        'change': float(data.get('P', 0)),
                        'timestamp': datetime.now()
                    }
            except Exception as e:
                logger.error(f"WebSocket message error: {e}")
        
        def on_error(ws, error):
            logger.error(f"WebSocket error: {error}")
        
        def on_close(ws, close_status_code, close_msg):
            logger.info("WebSocket connection closed")
        
        def on_open(ws):
            logger.info("WebSocket connection opened")
            # Subscribe to ticker streams
            streams = [f"{symbol.lower().replace('/', '')}@ticker" for symbol in symbols]
            subscribe_msg = {
                "method": "SUBSCRIBE",
                "params": streams,
                "id": 1
            }
            ws.send(json.dumps(subscribe_msg))
        
        # Binance WebSocket URL
        ws_url = "wss://stream.binance.com:9443/ws/!ticker@arr"
        
        ws = websocket.WebSocketApp(ws_url,
                                  on_open=on_open,
                                  on_message=on_message,
                                  on_error=on_error,
                                  on_close=on_close)
        
        # Run WebSocket in a separate thread
        ws_thread = threading.Thread(target=ws.run_forever)
        ws_thread.daemon = True
        ws_thread.start()
        
        return ws
    
    def get_market_data_batch(self, symbols, timeframe='1h'):
        """Get market data for multiple symbols"""
        market_data = {}
        
        for symbol in symbols:
            try:
                data = self.get_historical_data(symbol, timeframe)
                if data is not None:
                    market_data[symbol] = data
                time.sleep(0.1)  # Rate limiting
            except Exception as e:
                logger.error(f"Error fetching data for {symbol}: {e}")
        
        return market_data
    
    def get_order_book(self, symbol, limit=100):
        """Get order book data"""
        try:
            exchange = self.exchanges['binance_public']
            order_book = exchange.fetch_order_book(symbol, limit)
            return order_book
        except Exception as e:
            logger.error(f"Error fetching order book for {symbol}: {e}")
            return None
    
    def get_recent_trades(self, symbol, limit=100):
        """Get recent trades"""
        try:
            exchange = self.exchanges['binance_public']
            trades = exchange.fetch_trades(symbol, limit=limit)
            return trades
        except Exception as e:
            logger.error(f"Error fetching trades for {symbol}: {e}")
            return None
    
    def start_data_collection(self):
        """Start continuous data collection"""
        self.is_running = True
        
        # Start WebSocket streams
        self.start_websocket_stream(self.config.TRADING_PAIRS)
        
        logger.info("Data collection started")
    
    def stop_data_collection(self):
        """Stop data collection"""
        self.is_running = False
        logger.info("Data collection stopped")
    
    def get_cached_data(self, symbol):
        """Get cached real-time data"""
        return self.data_cache.get(symbol, None)
