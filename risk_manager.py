import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import logging
from config import Config

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RiskManager:
    def __init__(self):
        self.config = Config()
        self.portfolio_value = self.config.INITIAL_CAPITAL
        self.open_positions = {}
        self.daily_pnl = 0
        self.max_drawdown = 0
        self.peak_portfolio_value = self.portfolio_value
        self.trade_history = []
        
    def calculate_position_size(self, signal, current_portfolio_value):
        """Calculate optimal position size based on risk parameters"""
        try:
            # Base position size (percentage of portfolio)
            base_position_pct = self.config.RISK_PARAMS['max_position_size']
            
            # Adjust based on signal confidence and strength
            confidence_multiplier = signal['confidence']
            strength_multiplier = min(signal['signal_strength'] * 2, 1.0)
            
            # Technical confirmation adjustment
            tech_multiplier = {
                'STRONG': 1.0,
                'MODERATE': 0.8,
                'WEAK': 0.6,
                'NONE': 0.4,
                'ERROR': 0.3
            }.get(signal['technical_confirmation'], 0.5)
            
            # Calculate adjusted position size
            adjusted_position_pct = (
                base_position_pct * 
                confidence_multiplier * 
                strength_multiplier * 
                tech_multiplier
            )
            
            # Ensure minimum and maximum limits
            adjusted_position_pct = max(0.005, min(adjusted_position_pct, base_position_pct))
            
            # Calculate dollar amount
            position_value = current_portfolio_value * adjusted_position_pct
            
            # Calculate number of units based on current price
            units = position_value / signal['current_price']
            
            return {
                'units': units,
                'value': position_value,
                'percentage': adjusted_position_pct * 100,
                'confidence_factor': confidence_multiplier,
                'strength_factor': strength_multiplier,
                'technical_factor': tech_multiplier
            }
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return None
    
    def calculate_stop_loss_take_profit(self, signal, position_size):
        """Calculate stop loss and take profit levels"""
        try:
            current_price = signal['current_price']
            signal_type = signal['signal_type']
            
            if signal_type in ['BUY', 'STRONG_BUY']:
                # Long position
                stop_loss_price = current_price * (1 - self.config.RISK_PARAMS['stop_loss'])
                take_profit_price = current_price * (1 + self.config.RISK_PARAMS['take_profit'])
                
                # Adjust based on predicted price
                predicted_price = signal['predicted_price']
                if predicted_price > current_price:
                    # Use predicted price as take profit if it's higher
                    take_profit_price = max(take_profit_price, predicted_price)
                
            else:  # SELL, STRONG_SELL
                # Short position
                stop_loss_price = current_price * (1 + self.config.RISK_PARAMS['stop_loss'])
                take_profit_price = current_price * (1 - self.config.RISK_PARAMS['take_profit'])
                
                # Adjust based on predicted price
                predicted_price = signal['predicted_price']
                if predicted_price < current_price:
                    # Use predicted price as take profit if it's lower
                    take_profit_price = min(take_profit_price, predicted_price)
            
            # Calculate potential profit/loss
            if signal_type in ['BUY', 'STRONG_BUY']:
                max_loss = (current_price - stop_loss_price) * position_size['units']
                max_profit = (take_profit_price - current_price) * position_size['units']
            else:
                max_loss = (stop_loss_price - current_price) * position_size['units']
                max_profit = (current_price - take_profit_price) * position_size['units']
            
            return {
                'stop_loss_price': stop_loss_price,
                'take_profit_price': take_profit_price,
                'max_loss': max_loss,
                'max_profit': max_profit,
                'risk_reward_ratio': abs(max_profit / max_loss) if max_loss != 0 else 0
            }
            
        except Exception as e:
            logger.error(f"Error calculating stop loss/take profit: {e}")
            return None
    
    def validate_trade(self, signal, position_size, risk_levels):
        """Validate if trade meets risk management criteria"""
        validations = {
            'passed': True,
            'reasons': []
        }
        
        try:
            # Check maximum position size
            if position_size['percentage'] > self.config.RISK_PARAMS['max_position_size'] * 100:
                validations['passed'] = False
                validations['reasons'].append('Position size exceeds maximum limit')
            
            # Check maximum open positions
            if len(self.open_positions) >= self.config.RISK_PARAMS['max_open_positions']:
                validations['passed'] = False
                validations['reasons'].append('Maximum open positions reached')
            
            # Check daily loss limit
            daily_loss_pct = abs(self.daily_pnl) / self.portfolio_value
            if (self.daily_pnl < 0 and 
                daily_loss_pct >= self.config.RISK_PARAMS['max_daily_loss']):
                validations['passed'] = False
                validations['reasons'].append('Daily loss limit reached')
            
            # Check risk-reward ratio
            if risk_levels and risk_levels['risk_reward_ratio'] < 1.5:
                validations['passed'] = False
                validations['reasons'].append('Risk-reward ratio too low')
            
            # Check if symbol already has open position
            if signal['symbol'] in self.open_positions:
                validations['passed'] = False
                validations['reasons'].append('Position already open for this symbol')
            
            # Check minimum confidence threshold
            if signal['confidence'] < 0.7:
                validations['passed'] = False
                validations['reasons'].append('Signal confidence below minimum threshold')
            
            # Check technical confirmation
            if signal['technical_confirmation'] in ['NONE', 'ERROR']:
                validations['passed'] = False
                validations['reasons'].append('Insufficient technical confirmation')
            
            return validations
            
        except Exception as e:
            logger.error(f"Error validating trade: {e}")
            return {'passed': False, 'reasons': ['Validation error']}
    
    def create_trade_order(self, signal):
        """Create a complete trade order with risk management"""
        try:
            # Calculate position size
            position_size = self.calculate_position_size(signal, self.portfolio_value)
            if not position_size:
                return None
            
            # Calculate risk levels
            risk_levels = self.calculate_stop_loss_take_profit(signal, position_size)
            if not risk_levels:
                return None
            
            # Validate trade
            validation = self.validate_trade(signal, position_size, risk_levels)
            if not validation['passed']:
                logger.warning(f"Trade validation failed for {signal['symbol']}: {validation['reasons']}")
                return None
            
            # Create trade order
            trade_order = {
                'symbol': signal['symbol'],
                'signal_type': signal['signal_type'],
                'entry_price': signal['current_price'],
                'predicted_price': signal['predicted_price'],
                'position_size': position_size,
                'risk_levels': risk_levels,
                'confidence': signal['confidence'],
                'technical_confirmation': signal['technical_confirmation'],
                'timestamp': datetime.now(),
                'status': 'PENDING',
                'trade_id': f"{signal['symbol']}_{int(datetime.now().timestamp())}"
            }
            
            return trade_order
            
        except Exception as e:
            logger.error(f"Error creating trade order: {e}")
            return None
    
    def execute_trade(self, trade_order):
        """Execute trade and update portfolio"""
        try:
            trade_id = trade_order['trade_id']
            symbol = trade_order['symbol']
            
            # Add to open positions
            self.open_positions[symbol] = {
                'trade_id': trade_id,
                'symbol': symbol,
                'signal_type': trade_order['signal_type'],
                'entry_price': trade_order['entry_price'],
                'units': trade_order['position_size']['units'],
                'position_value': trade_order['position_size']['value'],
                'stop_loss': trade_order['risk_levels']['stop_loss_price'],
                'take_profit': trade_order['risk_levels']['take_profit_price'],
                'entry_time': datetime.now(),
                'unrealized_pnl': 0
            }
            
            # Update portfolio value (subtract position value for long, add for short)
            if trade_order['signal_type'] in ['BUY', 'STRONG_BUY']:
                self.portfolio_value -= trade_order['position_size']['value']
            
            # Add to trade history
            self.trade_history.append({
                'trade_id': trade_id,
                'action': 'OPEN',
                'symbol': symbol,
                'signal_type': trade_order['signal_type'],
                'price': trade_order['entry_price'],
                'units': trade_order['position_size']['units'],
                'value': trade_order['position_size']['value'],
                'timestamp': datetime.now()
            })
            
            logger.info(f"Trade executed: {trade_id} - {symbol} {trade_order['signal_type']}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error executing trade: {e}")
            return False
    
    def update_positions(self, current_prices):
        """Update open positions with current prices"""
        positions_to_close = []
        
        for symbol, position in self.open_positions.items():
            if symbol not in current_prices:
                continue
            
            current_price = current_prices[symbol]
            entry_price = position['entry_price']
            units = position['units']
            
            # Calculate unrealized P&L
            if position['signal_type'] in ['BUY', 'STRONG_BUY']:
                unrealized_pnl = (current_price - entry_price) * units
            else:
                unrealized_pnl = (entry_price - current_price) * units
            
            position['unrealized_pnl'] = unrealized_pnl
            position['current_price'] = current_price
            
            # Check stop loss and take profit
            should_close = False
            close_reason = ''
            
            if position['signal_type'] in ['BUY', 'STRONG_BUY']:
                if current_price <= position['stop_loss']:
                    should_close = True
                    close_reason = 'STOP_LOSS'
                elif current_price >= position['take_profit']:
                    should_close = True
                    close_reason = 'TAKE_PROFIT'
            else:
                if current_price >= position['stop_loss']:
                    should_close = True
                    close_reason = 'STOP_LOSS'
                elif current_price <= position['take_profit']:
                    should_close = True
                    close_reason = 'TAKE_PROFIT'
            
            if should_close:
                positions_to_close.append((symbol, close_reason))
        
        # Close positions that hit stop loss or take profit
        for symbol, reason in positions_to_close:
            self.close_position(symbol, reason)
    
    def close_position(self, symbol, reason='MANUAL'):
        """Close an open position"""
        if symbol not in self.open_positions:
            return False
        
        try:
            position = self.open_positions[symbol]
            realized_pnl = position['unrealized_pnl']
            
            # Update portfolio value
            if position['signal_type'] in ['BUY', 'STRONG_BUY']:
                self.portfolio_value += position['position_value'] + realized_pnl
            else:
                self.portfolio_value += realized_pnl
            
            # Update daily P&L
            self.daily_pnl += realized_pnl
            
            # Update max drawdown
            if self.portfolio_value > self.peak_portfolio_value:
                self.peak_portfolio_value = self.portfolio_value
            
            current_drawdown = (self.peak_portfolio_value - self.portfolio_value) / self.peak_portfolio_value
            self.max_drawdown = max(self.max_drawdown, current_drawdown)
            
            # Add to trade history
            self.trade_history.append({
                'trade_id': position['trade_id'],
                'action': 'CLOSE',
                'symbol': symbol,
                'signal_type': position['signal_type'],
                'price': position['current_price'],
                'units': position['units'],
                'pnl': realized_pnl,
                'reason': reason,
                'timestamp': datetime.now()
            })
            
            # Remove from open positions
            del self.open_positions[symbol]
            
            logger.info(f"Position closed: {symbol} - {reason} - P&L: ${realized_pnl:.2f}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error closing position {symbol}: {e}")
            return False
    
    def get_portfolio_summary(self):
        """Get current portfolio summary"""
        total_unrealized_pnl = sum(pos['unrealized_pnl'] for pos in self.open_positions.values())
        total_portfolio_value = self.portfolio_value + total_unrealized_pnl
        
        return {
            'total_value': total_portfolio_value,
            'cash': self.portfolio_value,
            'unrealized_pnl': total_unrealized_pnl,
            'daily_pnl': self.daily_pnl,
            'max_drawdown': self.max_drawdown * 100,
            'open_positions': len(self.open_positions),
            'total_trades': len(self.trade_history),
            'win_rate': self.calculate_win_rate()
        }
    
    def calculate_win_rate(self):
        """Calculate win rate from trade history"""
        closed_trades = [trade for trade in self.trade_history if trade['action'] == 'CLOSE' and 'pnl' in trade]
        
        if not closed_trades:
            return 0
        
        winning_trades = len([trade for trade in closed_trades if trade['pnl'] > 0])
        win_rate = winning_trades / len(closed_trades) * 100
        
        return win_rate
    
    def reset_daily_pnl(self):
        """Reset daily P&L (call at start of each day)"""
        self.daily_pnl = 0
