import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import logging
from config import Config
from ai_predictor import AIPredictor
from data_collector import CryptoDataCollector

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SignalGenerator:
    def __init__(self):
        self.config = Config()
        self.predictor = AIPredictor()
        self.data_collector = CryptoDataCollector()
        self.signals_history = {}
        self.current_signals = {}
        
    def generate_signal(self, symbol, current_price, prediction_data):
        """Generate trading signal based on AI prediction"""
        try:
            if not prediction_data:
                return None
            
            ensemble_pred = prediction_data['ensemble_prediction']
            confidence = prediction_data['confidence']
            
            # Calculate expected price change percentage
            price_change_pct = (ensemble_pred - current_price) / current_price
            
            # Determine signal strength based on price change and confidence
            signal_strength = abs(price_change_pct) * confidence
            
            # Generate signal based on thresholds
            if price_change_pct > 0:  # Bullish prediction
                if signal_strength >= 0.05 and confidence >= self.config.SIGNAL_THRESHOLDS['strong_buy']:
                    signal_type = 'STRONG_BUY'
                elif signal_strength >= 0.03 and confidence >= self.config.SIGNAL_THRESHOLDS['buy']:
                    signal_type = 'BUY'
                else:
                    signal_type = 'HOLD'
            else:  # Bearish prediction
                if signal_strength >= 0.05 and confidence >= self.config.SIGNAL_THRESHOLDS['strong_sell']:
                    signal_type = 'STRONG_SELL'
                elif signal_strength >= 0.03 and confidence >= self.config.SIGNAL_THRESHOLDS['sell']:
                    signal_type = 'SELL'
                else:
                    signal_type = 'HOLD'
            
            # Create signal object
            signal = {
                'symbol': symbol,
                'signal_type': signal_type,
                'current_price': current_price,
                'predicted_price': ensemble_pred,
                'price_change_pct': price_change_pct * 100,
                'confidence': confidence,
                'signal_strength': signal_strength,
                'timestamp': datetime.now(),
                'individual_predictions': prediction_data['individual_predictions']
            }
            
            # Add technical analysis confirmation
            signal = self.add_technical_confirmation(symbol, signal)
            
            return signal
            
        except Exception as e:
            logger.error(f"Error generating signal for {symbol}: {e}")
            return None
    
    def add_technical_confirmation(self, symbol, signal):
        """Add technical analysis confirmation to signal"""
        try:
            # Get recent historical data
            df = self.data_collector.get_historical_data(symbol, '1h', 100)
            if df is None or len(df) < 50:
                signal['technical_confirmation'] = 'INSUFFICIENT_DATA'
                return signal
            
            # Calculate technical indicators
            current_price = signal['current_price']
            
            # Moving averages
            sma_20 = df['close'].rolling(20).mean().iloc[-1]
            sma_50 = df['close'].rolling(50).mean().iloc[-1]
            ema_12 = df['close'].ewm(span=12).mean().iloc[-1]
            ema_26 = df['close'].ewm(span=26).mean().iloc[-1]
            
            # RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            current_rsi = rsi.iloc[-1]
            
            # MACD
            macd = ema_12 - ema_26
            macd_signal = macd.ewm(span=9).mean()
            macd_histogram = macd - macd_signal
            
            # Volume analysis
            avg_volume = df['volume'].rolling(20).mean().iloc[-1]
            current_volume = df['volume'].iloc[-1]
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1
            
            # Technical confirmation logic
            confirmations = []
            
            # Trend confirmation
            if signal['signal_type'] in ['BUY', 'STRONG_BUY']:
                if current_price > sma_20 > sma_50:
                    confirmations.append('UPTREND_CONFIRMED')
                if macd_histogram.iloc[-1] > 0:
                    confirmations.append('MACD_BULLISH')
                if current_rsi < 70:  # Not overbought
                    confirmations.append('RSI_NOT_OVERBOUGHT')
                if volume_ratio > 1.2:  # Above average volume
                    confirmations.append('VOLUME_SUPPORT')
                    
            elif signal['signal_type'] in ['SELL', 'STRONG_SELL']:
                if current_price < sma_20 < sma_50:
                    confirmations.append('DOWNTREND_CONFIRMED')
                if macd_histogram.iloc[-1] < 0:
                    confirmations.append('MACD_BEARISH')
                if current_rsi > 30:  # Not oversold
                    confirmations.append('RSI_NOT_OVERSOLD')
                if volume_ratio > 1.2:  # Above average volume
                    confirmations.append('VOLUME_SUPPORT')
            
            # Overall technical confirmation
            if len(confirmations) >= 3:
                technical_confirmation = 'STRONG'
            elif len(confirmations) >= 2:
                technical_confirmation = 'MODERATE'
            elif len(confirmations) >= 1:
                technical_confirmation = 'WEAK'
            else:
                technical_confirmation = 'NONE'
            
            signal['technical_confirmation'] = technical_confirmation
            signal['technical_details'] = {
                'confirmations': confirmations,
                'rsi': current_rsi,
                'macd_histogram': macd_histogram.iloc[-1],
                'price_vs_sma20': (current_price - sma_20) / sma_20 * 100,
                'volume_ratio': volume_ratio
            }
            
            return signal
            
        except Exception as e:
            logger.error(f"Error adding technical confirmation for {symbol}: {e}")
            signal['technical_confirmation'] = 'ERROR'
            return signal
    
    def filter_signals(self, signals):
        """Filter signals based on quality criteria"""
        filtered_signals = []
        
        for signal in signals:
            # Quality filters
            if signal['confidence'] < 0.6:
                continue
                
            if signal['signal_type'] == 'HOLD':
                continue
                
            if signal['technical_confirmation'] == 'NONE':
                continue
            
            # Avoid conflicting signals for same symbol
            symbol = signal['symbol']
            if symbol in [s['symbol'] for s in filtered_signals]:
                # Keep the signal with higher confidence
                existing_signal = next(s for s in filtered_signals if s['symbol'] == symbol)
                if signal['confidence'] > existing_signal['confidence']:
                    filtered_signals.remove(existing_signal)
                    filtered_signals.append(signal)
            else:
                filtered_signals.append(signal)
        
        return filtered_signals
    
    def generate_signals_for_all_pairs(self):
        """Generate signals for all trading pairs"""
        all_signals = []
        
        for symbol in self.config.TRADING_PAIRS:
            try:
                # Get current market data
                ticker = self.data_collector.get_real_time_data(symbol)
                if not ticker:
                    continue
                
                current_price = ticker['last']
                
                # Get historical data for prediction
                df = self.data_collector.get_historical_data(symbol, '1h', 200)
                if df is None or len(df) < 100:
                    continue
                
                # Prepare features for prediction
                X, y, features_df = self.predictor.prepare_data(df)
                if len(X) == 0:
                    continue
                
                # Make prediction using latest features
                latest_features = X[-1]
                prediction_data = self.predictor.predict(latest_features)
                
                if prediction_data:
                    # Generate signal
                    signal = self.generate_signal(symbol, current_price, prediction_data)
                    if signal:
                        all_signals.append(signal)
                        
            except Exception as e:
                logger.error(f"Error generating signal for {symbol}: {e}")
        
        # Filter and rank signals
        filtered_signals = self.filter_signals(all_signals)
        
        # Sort by signal strength and confidence
        filtered_signals.sort(key=lambda x: (x['signal_strength'] * x['confidence']), reverse=True)
        
        # Update current signals
        self.current_signals = {signal['symbol']: signal for signal in filtered_signals}
        
        # Store in history
        timestamp = datetime.now()
        self.signals_history[timestamp] = filtered_signals
        
        logger.info(f"Generated {len(filtered_signals)} high-quality signals")
        
        return filtered_signals
    
    def get_signal_summary(self, signals):
        """Get summary of generated signals"""
        if not signals:
            return "No signals generated"
        
        summary = {
            'total_signals': len(signals),
            'buy_signals': len([s for s in signals if s['signal_type'] in ['BUY', 'STRONG_BUY']]),
            'sell_signals': len([s for s in signals if s['signal_type'] in ['SELL', 'STRONG_SELL']]),
            'avg_confidence': np.mean([s['confidence'] for s in signals]),
            'strong_signals': len([s for s in signals if s['signal_strength'] > 0.05]),
            'top_signals': signals[:5]  # Top 5 signals
        }
        
        return summary
    
    def format_signal_message(self, signal):
        """Format signal for display/notification"""
        emoji_map = {
            'STRONG_BUY': '🚀',
            'BUY': '📈',
            'SELL': '📉',
            'STRONG_SELL': '💥',
            'HOLD': '⏸️'
        }
        
        emoji = emoji_map.get(signal['signal_type'], '❓')
        
        message = f"""
{emoji} **{signal['signal_type']}** - {signal['symbol']}
💰 Current Price: ${signal['current_price']:.4f}
🎯 Predicted Price: ${signal['predicted_price']:.4f}
📊 Expected Change: {signal['price_change_pct']:.2f}%
🎲 Confidence: {signal['confidence']:.1%}
🔧 Technical: {signal['technical_confirmation']}
⏰ Time: {signal['timestamp'].strftime('%H:%M:%S')}
        """
        
        return message.strip()
    
    def get_current_signals(self):
        """Get current active signals"""
        return self.current_signals
    
    def get_signals_history(self, hours=24):
        """Get signals history for specified hours"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        recent_signals = {
            timestamp: signals 
            for timestamp, signals in self.signals_history.items() 
            if timestamp >= cutoff_time
        }
        
        return recent_signals
