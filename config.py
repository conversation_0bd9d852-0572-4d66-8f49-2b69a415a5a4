import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # API Keys
    BINANCE_API_KEY = os.getenv('BINANCE_API_KEY', '')
    BINANCE_SECRET_KEY = os.getenv('BINANCE_SECRET_KEY', '')
    COINBASE_API_KEY = os.getenv('COINBASE_API_KEY', '')
    COINBASE_SECRET_KEY = os.getenv('COINBASE_SECRET_KEY', '')
    ALPHA_VANTAGE_API_KEY = os.getenv('ALPHA_VANTAGE_API_KEY', '')
    
    # Trading Pairs
    TRADING_PAIRS = [
        'BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'SOL/USDT',
        'XRP/USDT', 'DOT/USDT', 'DOGE/USDT', 'AVAX/USDT', 'LUNA/USDT'
    ]
    
    # Timeframes for analysis
    TIMEFRAMES = ['1m', '5m', '15m', '1h', '4h', '1d']
    
    # AI Model Parameters
    MODEL_PARAMS = {
        'lookback_period': 100,
        'prediction_horizon': 5,  # minutes
        'ensemble_models': ['xgboost', 'lightgbm', 'neural_network', 'lstm'],
        'feature_engineering': True,
        'technical_indicators': True,
        'sentiment_analysis': True
    }
    
    # Risk Management
    RISK_PARAMS = {
        'max_position_size': 0.02,  # 2% of portfolio per trade
        'stop_loss': 0.02,  # 2% stop loss
        'take_profit': 0.06,  # 6% take profit
        'max_daily_loss': 0.05,  # 5% max daily loss
        'max_open_positions': 5
    }
    
    # Signal Thresholds
    SIGNAL_THRESHOLDS = {
        'strong_buy': 0.85,
        'buy': 0.70,
        'hold': 0.50,
        'sell': 0.30,
        'strong_sell': 0.15
    }
    
    # Database
    DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite:///crypto_ai.db')
    
    # Redis for caching
    REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379')
    
    # Notification Settings
    TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN', '')
    TELEGRAM_CHAT_ID = os.getenv('TELEGRAM_CHAT_ID', '')
    DISCORD_WEBHOOK_URL = os.getenv('DISCORD_WEBHOOK_URL', '')
    
    # Dashboard Settings
    DASHBOARD_HOST = '0.0.0.0'
    DASHBOARD_PORT = 8080
    
    # Update intervals (seconds)
    DATA_UPDATE_INTERVAL = 60  # 1 minute
    PREDICTION_UPDATE_INTERVAL = 300  # 5 minutes
    SIGNAL_UPDATE_INTERVAL = 60  # 1 minute
    
    # Backtesting
    BACKTEST_START_DATE = '2023-01-01'
    BACKTEST_END_DATE = '2024-01-01'
    INITIAL_CAPITAL = 10000  # USD
