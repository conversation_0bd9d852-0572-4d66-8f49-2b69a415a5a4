#!/usr/bin/env python3
"""
Quick test script for AI Crypto Trading System
"""

import sys
import os
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_imports():
    """Test if all required modules can be imported"""
    print("🧪 Testing imports...")
    
    required_modules = [
        ('numpy', 'np'),
        ('pandas', 'pd'),
        ('sklearn', None),
        ('ccxt', None),
        ('flask', None)
    ]
    
    failed_imports = []
    
    for module_info in required_modules:
        module_name = module_info[0]
        alias = module_info[1]
        
        try:
            if alias:
                exec(f"import {module_name} as {alias}")
            else:
                exec(f"import {module_name}")
            print(f"   ✅ {module_name}")
        except ImportError as e:
            print(f"   ❌ {module_name}: {e}")
            failed_imports.append(module_name)
    
    if failed_imports:
        print(f"\n❌ Failed to import: {', '.join(failed_imports)}")
        print("💡 Install missing packages with: pip install -r requirements.txt")
        return False
    
    print("✅ All critical imports successful!")
    return True

def test_configuration():
    """Test configuration loading"""
    print("\n⚙️ Testing configuration...")
    
    try:
        from config import Config
        config = Config()
        
        print(f"   ✅ Configuration loaded")
        print(f"   📊 Trading pairs: {len(config.TRADING_PAIRS)}")
        print(f"   💰 Initial capital: ${config.INITIAL_CAPITAL:,}")
        print(f"   🎯 Max position size: {config.RISK_PARAMS['max_position_size']*100}%")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Configuration error: {e}")
        return False

def test_data_collector():
    """Test data collection functionality"""
    print("\n📊 Testing data collector...")
    
    try:
        from data_collector import CryptoDataCollector
        
        data_collector = CryptoDataCollector()
        print("   ✅ Data collector initialized")
        
        # Test historical data fetching (public API, no keys needed)
        print("   📈 Testing historical data fetch...")
        df = data_collector.get_historical_data('BTC/USDT', '1h', 10)
        
        if df is not None and len(df) > 0:
            latest_price = df['close'].iloc[-1]
            print(f"   ✅ Fetched {len(df)} data points for BTC/USDT")
            print(f"   💰 Latest BTC price: ${latest_price:,.2f}")
            
            # Test data quality
            if df['close'].min() > 0 and df['volume'].sum() > 0:
                print("   ✅ Data quality check passed")
            else:
                print("   ⚠️  Data quality issues detected")
                
        else:
            print("   ⚠️  No data fetched (check internet connection)")
            
        return True
        
    except Exception as e:
        print(f"   ❌ Data collector error: {e}")
        return False

def test_ai_predictor():
    """Test AI predictor initialization"""
    print("\n🧠 Testing AI predictor...")
    
    try:
        from ai_predictor import AIPredictor
        
        predictor = AIPredictor()
        print("   ✅ AI predictor initialized")
        
        # Test feature creation with sample data
        import pandas as pd
        import numpy as np
        
        # Create sample OHLCV data
        dates = pd.date_range(start='2024-01-01', periods=100, freq='H')
        sample_data = pd.DataFrame({
            'open': np.random.uniform(40000, 50000, 100),
            'high': np.random.uniform(45000, 55000, 100),
            'low': np.random.uniform(35000, 45000, 100),
            'close': np.random.uniform(40000, 50000, 100),
            'volume': np.random.uniform(100, 1000, 100)
        }, index=dates)
        
        # Test feature engineering
        features_df = predictor.create_technical_features(sample_data)
        
        if len(features_df.columns) > 10:
            print(f"   ✅ Created {len(features_df.columns)} features")
            print("   ✅ Feature engineering working")
        else:
            print("   ⚠️  Limited features created")
            
        return True
        
    except Exception as e:
        print(f"   ❌ AI predictor error: {e}")
        return False

def test_signal_generator():
    """Test signal generator"""
    print("\n🎯 Testing signal generator...")
    
    try:
        from signal_generator import SignalGenerator
        
        signal_generator = SignalGenerator()
        print("   ✅ Signal generator initialized")
        
        # Test signal formatting
        sample_signal = {
            'symbol': 'BTC/USDT',
            'signal_type': 'BUY',
            'current_price': 45000.0,
            'predicted_price': 46500.0,
            'price_change_pct': 3.33,
            'confidence': 0.85,
            'technical_confirmation': 'STRONG',
            'timestamp': datetime.now()
        }
        
        message = signal_generator.format_signal_message(sample_signal)
        
        if 'BUY' in message and 'BTC/USDT' in message:
            print("   ✅ Signal formatting working")
        else:
            print("   ⚠️  Signal formatting issues")
            
        return True
        
    except Exception as e:
        print(f"   ❌ Signal generator error: {e}")
        return False

def test_risk_manager():
    """Test risk manager"""
    print("\n🛡️ Testing risk manager...")
    
    try:
        from risk_manager import RiskManager
        
        risk_manager = RiskManager()
        print("   ✅ Risk manager initialized")
        
        # Test portfolio summary
        portfolio = risk_manager.get_portfolio_summary()
        
        if 'total_value' in portfolio and 'win_rate' in portfolio:
            print(f"   ✅ Portfolio value: ${portfolio['total_value']:,.2f}")
            print(f"   ✅ Win rate: {portfolio['win_rate']:.1f}%")
        else:
            print("   ⚠️  Portfolio summary issues")
            
        return True
        
    except Exception as e:
        print(f"   ❌ Risk manager error: {e}")
        return False

def test_dashboard():
    """Test dashboard creation"""
    print("\n📊 Testing dashboard...")
    
    try:
        from dashboard import create_dashboard
        
        # Create a mock trading system object
        class MockTradingSystem:
            def __init__(self):
                from config import Config
                self.config = Config()
        
        mock_system = MockTradingSystem()
        app = create_dashboard(mock_system)
        
        if app:
            print("   ✅ Dashboard created successfully")
            print("   🌐 Dashboard will be available at http://localhost:8080")
        else:
            print("   ⚠️  Dashboard creation issues")
            
        return True
        
    except Exception as e:
        print(f"   ❌ Dashboard error: {e}")
        return False

def test_file_structure():
    """Test file structure"""
    print("\n📁 Testing file structure...")
    
    required_files = [
        'config.py',
        'data_collector.py',
        'ai_predictor.py',
        'signal_generator.py',
        'risk_manager.py',
        'dashboard.py',
        'main.py',
        'requirements.txt',
        '.env.example'
    ]
    
    required_dirs = [
        'models',
        'data',
        'logs',
        'templates'
    ]
    
    missing_files = []
    missing_dirs = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
        else:
            print(f"   ✅ {file}")
    
    for directory in required_dirs:
        if not os.path.exists(directory):
            missing_dirs.append(directory)
        else:
            print(f"   ✅ {directory}/")
    
    if missing_files:
        print(f"   ❌ Missing files: {', '.join(missing_files)}")
        return False
    
    if missing_dirs:
        print(f"   ❌ Missing directories: {', '.join(missing_dirs)}")
        return False
    
    print("   ✅ All required files and directories present")
    return True

def main():
    """Main test function"""
    print("🚀 AI CRYPTO TRADING SYSTEM - SYSTEM TEST")
    print("=" * 60)
    print("Testing system components and functionality...")
    print()
    
    tests = [
        ("File Structure", test_file_structure),
        ("Python Imports", test_imports),
        ("Configuration", test_configuration),
        ("Data Collector", test_data_collector),
        ("AI Predictor", test_ai_predictor),
        ("Signal Generator", test_signal_generator),
        ("Risk Manager", test_risk_manager),
        ("Dashboard", test_dashboard)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
            else:
                print(f"   ⚠️  {test_name} test had issues")
        except Exception as e:
            print(f"   ❌ {test_name} test failed: {e}")
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS")
    print("=" * 60)
    print(f"✅ Passed: {passed_tests}/{total_tests} tests")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! System is ready to use.")
        print("\n📋 NEXT STEPS:")
        print("1. Configure API keys in .env file")
        print("2. Run: python main.py")
        print("3. Access dashboard: http://localhost:8080")
    elif passed_tests >= total_tests * 0.7:
        print("⚠️  Most tests passed. System should work with minor issues.")
        print("💡 Check the failed tests and install missing dependencies.")
    else:
        print("❌ Multiple test failures. Please fix issues before proceeding.")
        print("💡 Run: pip install -r requirements.txt")
    
    print("\n🔧 TROUBLESHOOTING:")
    print("- Install dependencies: pip install -r requirements.txt")
    print("- Check Python version: python --version (3.8+ required)")
    print("- Verify internet connection for data fetching")
    print("- Review error messages above for specific issues")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
