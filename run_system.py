#!/usr/bin/env python3
"""
Simple launcher for the AI Crypto Trading System
This script handles basic setup and launches the system
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def print_banner():
    """Print system banner"""
    print("🚀 AI CRYPTO TRADING SYSTEM")
    print("=" * 50)
    print("High-Performance Cryptocurrency Trading AI")
    print("Real-time Analysis • Smart Signals • Risk Management")
    print("=" * 50)
    print()

def check_python_version():
    """Check Python version"""
    if sys.version_info < (3, 7):
        print("❌ Python 3.7+ required!")
        print(f"Current version: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version.split()[0]}")
    return True

def install_basic_dependencies():
    """Install basic dependencies if missing"""
    basic_packages = [
        'pandas',
        'numpy', 
        'requests',
        'flask',
        'ccxt'
    ]
    
    missing_packages = []
    
    for package in basic_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"📦 Installing missing packages: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install'
            ] + missing_packages)
            print("✅ Basic packages installed")
        except subprocess.CalledProcessError:
            print("❌ Failed to install packages")
            print("💡 Try manually: pip install pandas numpy requests flask ccxt")
            return False
    
    return True

def create_basic_env():
    """Create basic .env file if it doesn't exist"""
    if not os.path.exists('.env'):
        env_content = """# AI Crypto Trading System Configuration
# Copy this file to .env and configure your settings

# Exchange API Keys (Optional for demo mode)
BINANCE_API_KEY=
BINANCE_SECRET_KEY=

# Notification Settings (Optional)
TELEGRAM_BOT_TOKEN=
TELEGRAM_CHAT_ID=

# Trading Settings
INITIAL_CAPITAL=10000
MAX_POSITION_SIZE=0.02
STOP_LOSS=0.02
TAKE_PROFIT=0.06

# Dashboard
DASHBOARD_PORT=8080
"""
        
        with open('.env', 'w') as f:
            f.write(env_content)
        
        print("✅ Created .env configuration file")
    
    return True

def run_simple_demo():
    """Run a simple demo without full dependencies"""
    print("🎯 Running Simple Demo Mode")
    print("-" * 30)
    
    try:
        import pandas as pd
        import numpy as np
        import requests
        from datetime import datetime, timedelta
        
        # Simple crypto price fetching
        print("📊 Fetching Bitcoin price...")
        
        try:
            # Use a simple API that doesn't require keys
            response = requests.get(
                'https://api.coinbase.com/v2/exchange-rates?currency=BTC',
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                btc_price = float(data['data']['rates']['USD'])
                print(f"💰 Current BTC Price: ${btc_price:,.2f}")
                
                # Simple trend analysis
                print("📈 Performing simple trend analysis...")
                
                # Simulate some basic analysis
                trend_score = np.random.uniform(0.6, 0.9)
                signal_type = "BUY" if trend_score > 0.75 else "HOLD"
                
                print(f"🎯 AI Signal: {signal_type}")
                print(f"🎲 Confidence: {trend_score:.1%}")
                
                if signal_type == "BUY":
                    print("📈 Bullish trend detected!")
                else:
                    print("⏸️  Neutral market conditions")
                
            else:
                print("⚠️  Could not fetch price data")
                
        except Exception as e:
            print(f"⚠️  API Error: {e}")
            print("💡 Using simulated data for demo...")
            
            # Simulated demo
            btc_price = 45000 + np.random.uniform(-2000, 2000)
            print(f"💰 Simulated BTC Price: ${btc_price:,.2f}")
            print("🎯 AI Signal: BUY")
            print("🎲 Confidence: 78.5%")
            print("📈 Bullish trend detected!")
        
        print("\n✅ Demo completed successfully!")
        print("💡 This is a simplified demo. For full functionality:")
        print("   1. Install all dependencies: pip install -r requirements.txt")
        print("   2. Configure API keys in .env file")
        print("   3. Run: python main.py")
        
        return True
        
    except ImportError as e:
        print(f"❌ Missing required package: {e}")
        return False
    except Exception as e:
        print(f"❌ Demo error: {e}")
        return False

def run_full_system():
    """Run the full trading system"""
    print("🚀 Starting Full AI Trading System...")
    
    try:
        # Import and run the main system
        from main import CryptoAITradingSystem
        
        trading_system = CryptoAITradingSystem()
        
        if trading_system.start_system():
            print("✅ System started successfully!")
            print(f"📊 Dashboard: http://localhost:8080")
            print("Press Ctrl+C to stop")
            
            # Keep running
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 Shutting down...")
                trading_system.stop_system()
                
        else:
            print("❌ Failed to start system")
            return False
            
    except ImportError as e:
        print(f"❌ Missing dependencies: {e}")
        print("💡 Install with: pip install -r requirements.txt")
        return False
    except Exception as e:
        print(f"❌ System error: {e}")
        return False
    
    return True

def main():
    """Main launcher function"""
    print_banner()
    
    # Basic checks
    if not check_python_version():
        return False
    
    # Create basic environment
    create_basic_env()
    
    # Check what mode to run
    print("🔍 Checking system capabilities...")
    
    # Try to import main dependencies
    try:
        import sklearn
        import tensorflow
        import xgboost
        full_system_available = True
        print("✅ Full system dependencies available")
    except ImportError:
        full_system_available = False
        print("⚠️  Full system dependencies not available")
    
    # Install basic dependencies
    if not install_basic_dependencies():
        print("❌ Could not install basic dependencies")
        return False
    
    print("\n📋 SYSTEM OPTIONS:")
    print("1. 🎯 Simple Demo (Basic functionality)")
    print("2. 🚀 Full System (Complete AI trading)")
    print("3. 🧪 System Test")
    print("4. ❌ Exit")
    
    while True:
        try:
            choice = input("\nSelect option (1-4): ").strip()
            
            if choice == '1':
                print("\n" + "="*50)
                return run_simple_demo()
                
            elif choice == '2':
                if full_system_available:
                    print("\n" + "="*50)
                    return run_full_system()
                else:
                    print("❌ Full system not available")
                    print("💡 Install dependencies: pip install -r requirements.txt")
                    continue
                    
            elif choice == '3':
                print("\n" + "="*50)
                try:
                    import test_system
                    return test_system.main()
                except ImportError:
                    print("❌ Test system not available")
                    return False
                    
            elif choice == '4':
                print("👋 Goodbye!")
                return True
                
            else:
                print("❌ Invalid choice. Please select 1-4.")
                continue
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            return True
        except Exception as e:
            print(f"❌ Error: {e}")
            continue

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
