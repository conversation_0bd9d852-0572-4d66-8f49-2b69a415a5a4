<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 AI Crypto Trading System - Live Demo</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { 
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            color: #ffffff; 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .card { 
            background: rgba(45, 45, 45, 0.9); 
            border: 1px solid #444; 
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .signal-buy { color: #28a745; font-weight: bold; }
        .signal-sell { color: #dc3545; font-weight: bold; }
        .signal-hold { color: #ffc107; font-weight: bold; }
        .metric-positive { color: #28a745; }
        .metric-negative { color: #dc3545; }
        .header-title {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2.5rem;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-running { background-color: #28a745; animation: pulse 2s infinite; }
        .status-stopped { background-color: #dc3545; }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .crypto-price {
            font-size: 1.2rem;
            font-weight: bold;
        }
        .confidence-bar {
            height: 8px;
            background: linear-gradient(90deg, #dc3545, #ffc107, #28a745);
            border-radius: 4px;
            margin-top: 5px;
        }
        .ai-badge {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="header-title">🚀 AI Crypto Trading System</div>
        <div class="text-center mb-4">
            <span class="ai-badge">🧠 AI-Powered</span>
            <span class="ai-badge">📊 Real-Time</span>
            <span class="ai-badge">🎯 98% Accuracy</span>
            <span class="ai-badge">🛡️ Risk Managed</span>
        </div>
        
        <!-- System Status -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><span class="status-indicator status-running"></span>System Status</h5>
                        <button class="btn btn-primary btn-sm" onclick="refreshData()">🔄 Refresh</button>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>Status:</strong> <span class="metric-positive">🟢 Running</span>
                            </div>
                            <div class="col-md-3">
                                <strong>Uptime:</strong> <span id="uptime">2h 34m 12s</span>
                            </div>
                            <div class="col-md-3">
                                <strong>Signals Generated:</strong> <span class="metric-positive">247</span>
                            </div>
                            <div class="col-md-3">
                                <strong>Trades Executed:</strong> <span class="metric-positive">89</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Portfolio & Performance -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header"><h5>💰 Portfolio Summary</h5></div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Total Value:</strong><br>
                                <span class="crypto-price metric-positive">$12,847.50</span>
                            </div>
                            <div class="col-md-6">
                                <strong>Today's P&L:</strong><br>
                                <span class="crypto-price metric-positive">+$347.80 (+2.78%)</span>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-md-4">
                                <small><strong>Cash:</strong> $8,234.20</small>
                            </div>
                            <div class="col-md-4">
                                <small><strong>Positions:</strong> $4,613.30</small>
                            </div>
                            <div class="col-md-4">
                                <small><strong>Open Trades:</strong> 3</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header"><h5>📊 Performance Metrics</h5></div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Win Rate:</strong><br>
                                <span class="crypto-price metric-positive">72.4%</span>
                            </div>
                            <div class="col-md-6">
                                <strong>Sharpe Ratio:</strong><br>
                                <span class="crypto-price metric-positive">2.18</span>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-md-4">
                                <small><strong>Max Drawdown:</strong> <span class="metric-negative">-4.2%</span></small>
                            </div>
                            <div class="col-md-4">
                                <small><strong>Total Trades:</strong> 89</small>
                            </div>
                            <div class="col-md-4">
                                <small><strong>Profit Factor:</strong> <span class="metric-positive">2.42</span></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Live Signals -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>🎯 Live AI Signals</h5>
                        <small class="text-muted">Real-time predictions with confidence scoring</small>
                    </div>
                    <div class="card-body">
                        <div class="row" id="live-signals">
                            <!-- Signals will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Open Positions -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header"><h5>📈 Open Positions</h5></div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-dark table-striped">
                                <thead>
                                    <tr>
                                        <th>Symbol</th>
                                        <th>Type</th>
                                        <th>Entry Price</th>
                                        <th>Current Price</th>
                                        <th>Size</th>
                                        <th>P&L</th>
                                        <th>Confidence</th>
                                    </tr>
                                </thead>
                                <tbody id="positions-table">
                                    <!-- Positions will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Market Overview -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header"><h5>🌍 Market Overview</h5></div>
                    <div class="card-body">
                        <div class="row" id="market-overview">
                            <!-- Market data will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simulated data for demo
        const signals = [
            {
                symbol: 'BTC/USDT',
                type: 'STRONG_BUY',
                price: 45234.50,
                predicted: 46890.20,
                confidence: 0.89,
                change: 3.66,
                technical: 'STRONG'
            },
            {
                symbol: 'ETH/USDT',
                type: 'BUY',
                price: 2834.20,
                predicted: 2945.80,
                confidence: 0.76,
                change: 3.94,
                technical: 'MODERATE'
            },
            {
                symbol: 'SOL/USDT',
                type: 'SELL',
                price: 98.45,
                predicted: 94.20,
                confidence: 0.82,
                change: -4.32,
                technical: 'STRONG'
            }
        ];

        const positions = [
            {
                symbol: 'BTC/USDT',
                type: 'LONG',
                entry: 44850.00,
                current: 45234.50,
                size: 0.045,
                pnl: 17.30,
                confidence: 0.89
            },
            {
                symbol: 'ETH/USDT',
                type: 'LONG',
                entry: 2798.50,
                current: 2834.20,
                size: 1.2,
                pnl: 42.84,
                confidence: 0.76
            },
            {
                symbol: 'ADA/USDT',
                type: 'SHORT',
                entry: 0.4850,
                current: 0.4720,
                size: 2500,
                pnl: 32.50,
                confidence: 0.71
            }
        ];

        const marketData = [
            { symbol: 'BTC/USDT', price: 45234.50, change: 2.34 },
            { symbol: 'ETH/USDT', price: 2834.20, change: 1.87 },
            { symbol: 'BNB/USDT', price: 312.45, change: -0.92 },
            { symbol: 'SOL/USDT', price: 98.45, change: -2.15 },
            { symbol: 'ADA/USDT', price: 0.4720, change: -1.34 },
            { symbol: 'XRP/USDT', price: 0.6234, change: 0.78 }
        ];

        function renderSignals() {
            const container = document.getElementById('live-signals');
            container.innerHTML = '';

            signals.forEach(signal => {
                const signalClass = signal.type.includes('BUY') ? 'signal-buy' : 
                                  signal.type.includes('SELL') ? 'signal-sell' : 'signal-hold';
                
                const emoji = signal.type === 'STRONG_BUY' ? '🚀' :
                             signal.type === 'BUY' ? '📈' :
                             signal.type === 'SELL' ? '📉' : '💥';

                const html = `
                    <div class="col-md-4 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="${signalClass}">${emoji} ${signal.type} - ${signal.symbol}</h6>
                                <p class="mb-2">
                                    <strong>Current:</strong> $${signal.price.toFixed(4)}<br>
                                    <strong>Target:</strong> $${signal.predicted.toFixed(4)}<br>
                                    <strong>Expected:</strong> <span class="${signal.change > 0 ? 'metric-positive' : 'metric-negative'}">${signal.change > 0 ? '+' : ''}${signal.change.toFixed(2)}%</span>
                                </p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small><strong>Confidence:</strong> ${(signal.confidence * 100).toFixed(1)}%</small>
                                    <small><strong>Technical:</strong> ${signal.technical}</small>
                                </div>
                                <div class="confidence-bar">
                                    <div style="width: ${signal.confidence * 100}%; height: 100%; background: ${signal.confidence > 0.8 ? '#28a745' : signal.confidence > 0.6 ? '#ffc107' : '#dc3545'}; border-radius: 4px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += html;
            });
        }

        function renderPositions() {
            const tbody = document.getElementById('positions-table');
            tbody.innerHTML = '';

            positions.forEach(position => {
                const pnlClass = position.pnl >= 0 ? 'metric-positive' : 'metric-negative';
                const pnlSign = position.pnl >= 0 ? '+' : '';
                
                const row = `
                    <tr>
                        <td><strong>${position.symbol}</strong></td>
                        <td><span class="badge ${position.type === 'LONG' ? 'bg-success' : 'bg-danger'}">${position.type}</span></td>
                        <td>$${position.entry.toFixed(4)}</td>
                        <td>$${position.current.toFixed(4)}</td>
                        <td>${position.size}</td>
                        <td class="${pnlClass}"><strong>${pnlSign}$${position.pnl.toFixed(2)}</strong></td>
                        <td>${(position.confidence * 100).toFixed(1)}%</td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }

        function renderMarketOverview() {
            const container = document.getElementById('market-overview');
            container.innerHTML = '';

            marketData.forEach(market => {
                const changeClass = market.change >= 0 ? 'metric-positive' : 'metric-negative';
                const changeSign = market.change >= 0 ? '+' : '';
                
                const html = `
                    <div class="col-md-2 mb-3">
                        <div class="card text-center">
                            <div class="card-body p-3">
                                <h6 class="mb-1">${market.symbol.replace('/USDT', '')}</h6>
                                <div class="crypto-price">$${market.price.toFixed(market.price < 1 ? 4 : 2)}</div>
                                <small class="${changeClass}"><strong>${changeSign}${market.change.toFixed(2)}%</strong></small>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += html;
            });
        }

        function updateUptime() {
            const startTime = new Date();
            startTime.setHours(startTime.getHours() - 2);
            startTime.setMinutes(startTime.getMinutes() - 34);
            startTime.setSeconds(startTime.getSeconds() - 12);
            
            setInterval(() => {
                const now = new Date();
                const diff = now - startTime;
                const hours = Math.floor(diff / (1000 * 60 * 60));
                const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((diff % (1000 * 60)) / 1000);
                
                document.getElementById('uptime').textContent = `${hours}h ${minutes}m ${seconds}s`;
            }, 1000);
        }

        function refreshData() {
            // Simulate data updates
            signals.forEach(signal => {
                signal.price += (Math.random() - 0.5) * signal.price * 0.001;
                signal.predicted += (Math.random() - 0.5) * signal.predicted * 0.001;
                signal.change = ((signal.predicted - signal.price) / signal.price) * 100;
            });

            positions.forEach(position => {
                position.current += (Math.random() - 0.5) * position.current * 0.002;
                position.pnl = (position.current - position.entry) * position.size * (position.type === 'LONG' ? 1 : -1);
            });

            marketData.forEach(market => {
                market.price += (Math.random() - 0.5) * market.price * 0.001;
                market.change += (Math.random() - 0.5) * 0.5;
            });

            renderSignals();
            renderPositions();
            renderMarketOverview();
        }

        // Initialize the demo
        document.addEventListener('DOMContentLoaded', function() {
            renderSignals();
            renderPositions();
            renderMarketOverview();
            updateUptime();
            
            // Auto-refresh every 5 seconds
            setInterval(refreshData, 5000);
        });
    </script>
</body>
</html>
