# API Keys for Cryptocurrency Exchanges
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here
COINBASE_API_KEY=your_coinbase_api_key_here
COINBASE_SECRET_KEY=your_coinbase_secret_key_here
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key_here

# Database Configuration
DATABASE_URL=sqlite:///crypto_ai.db
REDIS_URL=redis://localhost:6379

# Notification Settings
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here
DISCORD_WEBHOOK_URL=your_discord_webhook_url_here

# Email Settings (Optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password_here

# Trading Settings
INITIAL_CAPITAL=10000
MAX_POSITION_SIZE=0.02
STOP_LOSS=0.02
TAKE_PROFIT=0.06
MAX_DAILY_LOSS=0.05

# AI Model Settings
MODEL_RETRAIN_INTERVAL=24  # hours
PREDICTION_CONFIDENCE_THRESHOLD=0.7
SIGNAL_GENERATION_INTERVAL=300  # seconds

# Dashboard Settings
DASHBOARD_HOST=0.0.0.0
DASHBOARD_PORT=8080

# Logging
LOG_LEVEL=INFO
LOG_FILE=crypto_ai.log
