import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_squared_error, mean_absolute_error
import xgboost as xgb
import lightgbm as lgb
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout, Conv1D, MaxPooling1D, Flatten
import talib
import joblib
import logging
from datetime import datetime, timedelta
from config import Config

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AIPredictor:
    def __init__(self):
        self.config = Config()
        self.models = {}
        self.scalers = {}
        self.feature_columns = []
        self.is_trained = False
        
    def create_technical_features(self, df):
        """Create technical analysis features"""
        features_df = df.copy()
        
        # Price-based features
        features_df['returns'] = df['close'].pct_change()
        features_df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        features_df['price_change'] = df['close'] - df['open']
        features_df['high_low_ratio'] = df['high'] / df['low']
        features_df['volume_price_trend'] = df['volume'] * features_df['returns']
        
        # Moving averages
        for period in [5, 10, 20, 50, 100]:
            features_df[f'sma_{period}'] = df['close'].rolling(window=period).mean()
            features_df[f'ema_{period}'] = df['close'].ewm(span=period).mean()
            features_df[f'price_sma_{period}_ratio'] = df['close'] / features_df[f'sma_{period}']
        
        # Technical indicators using TA-Lib
        try:
            # RSI
            features_df['rsi'] = talib.RSI(df['close'].values, timeperiod=14)
            features_df['rsi_oversold'] = (features_df['rsi'] < 30).astype(int)
            features_df['rsi_overbought'] = (features_df['rsi'] > 70).astype(int)
            
            # MACD
            macd, macd_signal, macd_hist = talib.MACD(df['close'].values)
            features_df['macd'] = macd
            features_df['macd_signal'] = macd_signal
            features_df['macd_histogram'] = macd_hist
            
            # Bollinger Bands
            bb_upper, bb_middle, bb_lower = talib.BBANDS(df['close'].values)
            features_df['bb_upper'] = bb_upper
            features_df['bb_lower'] = bb_lower
            features_df['bb_width'] = (bb_upper - bb_lower) / bb_middle
            features_df['bb_position'] = (df['close'] - bb_lower) / (bb_upper - bb_lower)
            
            # Stochastic
            slowk, slowd = talib.STOCH(df['high'].values, df['low'].values, df['close'].values)
            features_df['stoch_k'] = slowk
            features_df['stoch_d'] = slowd
            
            # ADX
            features_df['adx'] = talib.ADX(df['high'].values, df['low'].values, df['close'].values)
            
            # Williams %R
            features_df['williams_r'] = talib.WILLR(df['high'].values, df['low'].values, df['close'].values)
            
            # CCI
            features_df['cci'] = talib.CCI(df['high'].values, df['low'].values, df['close'].values)
            
        except Exception as e:
            logger.warning(f"Error calculating technical indicators: {e}")
        
        # Volume indicators
        features_df['volume_sma'] = df['volume'].rolling(window=20).mean()
        features_df['volume_ratio'] = df['volume'] / features_df['volume_sma']
        
        # Volatility
        features_df['volatility'] = features_df['returns'].rolling(window=20).std()
        features_df['atr'] = talib.ATR(df['high'].values, df['low'].values, df['close'].values)
        
        # Time-based features
        features_df['hour'] = df.index.hour
        features_df['day_of_week'] = df.index.dayofweek
        features_df['month'] = df.index.month
        
        return features_df
    
    def create_sequences(self, data, lookback_period, prediction_horizon):
        """Create sequences for time series prediction"""
        X, y = [], []
        
        for i in range(lookback_period, len(data) - prediction_horizon + 1):
            X.append(data[i-lookback_period:i])
            y.append(data[i + prediction_horizon - 1])
        
        return np.array(X), np.array(y)
    
    def prepare_data(self, df, target_column='close'):
        """Prepare data for training"""
        # Create technical features
        features_df = self.create_technical_features(df)
        
        # Remove NaN values
        features_df = features_df.dropna()
        
        # Select feature columns (exclude target and timestamp)
        exclude_cols = [target_column, 'open', 'high', 'low', 'volume']
        self.feature_columns = [col for col in features_df.columns if col not in exclude_cols]
        
        X = features_df[self.feature_columns].values
        y = features_df[target_column].values
        
        return X, y, features_df
    
    def train_ensemble_models(self, X, y):
        """Train ensemble of ML models"""
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # Scale features
        self.scalers['standard'] = StandardScaler()
        X_train_scaled = self.scalers['standard'].fit_transform(X_train)
        X_test_scaled = self.scalers['standard'].transform(X_test)
        
        # XGBoost
        logger.info("Training XGBoost model...")
        self.models['xgboost'] = xgb.XGBRegressor(
            n_estimators=200,
            max_depth=6,
            learning_rate=0.1,
            random_state=42
        )
        self.models['xgboost'].fit(X_train_scaled, y_train)
        
        # LightGBM
        logger.info("Training LightGBM model...")
        self.models['lightgbm'] = lgb.LGBMRegressor(
            n_estimators=200,
            max_depth=6,
            learning_rate=0.1,
            random_state=42
        )
        self.models['lightgbm'].fit(X_train_scaled, y_train)
        
        # Random Forest
        logger.info("Training Random Forest model...")
        self.models['random_forest'] = RandomForestRegressor(
            n_estimators=100,
            max_depth=10,
            random_state=42
        )
        self.models['random_forest'].fit(X_train_scaled, y_train)
        
        # Gradient Boosting
        logger.info("Training Gradient Boosting model...")
        self.models['gradient_boosting'] = GradientBoostingRegressor(
            n_estimators=100,
            max_depth=6,
            learning_rate=0.1,
            random_state=42
        )
        self.models['gradient_boosting'].fit(X_train_scaled, y_train)
        
        # Evaluate models
        self.evaluate_models(X_test_scaled, y_test)
        
        self.is_trained = True
        logger.info("All models trained successfully!")
    
    def train_lstm_model(self, data, lookback_period=60):
        """Train LSTM neural network"""
        # Prepare data for LSTM
        scaler = MinMaxScaler()
        scaled_data = scaler.fit_transform(data.reshape(-1, 1))
        
        X, y = self.create_sequences(scaled_data.flatten(), lookback_period, 1)
        
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # Reshape for LSTM
        X_train = X_train.reshape((X_train.shape[0], X_train.shape[1], 1))
        X_test = X_test.reshape((X_test.shape[0], X_test.shape[1], 1))
        
        # Build LSTM model
        model = Sequential([
            LSTM(50, return_sequences=True, input_shape=(lookback_period, 1)),
            Dropout(0.2),
            LSTM(50, return_sequences=False),
            Dropout(0.2),
            Dense(25),
            Dense(1)
        ])
        
        model.compile(optimizer='adam', loss='mean_squared_error')
        
        # Train model
        logger.info("Training LSTM model...")
        model.fit(X_train, y_train, batch_size=32, epochs=50, validation_split=0.1, verbose=0)
        
        self.models['lstm'] = model
        self.scalers['lstm'] = scaler
        
        logger.info("LSTM model trained successfully!")
    
    def evaluate_models(self, X_test, y_test):
        """Evaluate model performance"""
        results = {}
        
        for name, model in self.models.items():
            if name != 'lstm':  # Skip LSTM for now
                try:
                    y_pred = model.predict(X_test)
                    mse = mean_squared_error(y_test, y_pred)
                    mae = mean_absolute_error(y_test, y_pred)
                    
                    results[name] = {
                        'mse': mse,
                        'mae': mae,
                        'rmse': np.sqrt(mse)
                    }
                    
                    logger.info(f"{name} - MSE: {mse:.4f}, MAE: {mae:.4f}, RMSE: {np.sqrt(mse):.4f}")
                except Exception as e:
                    logger.error(f"Error evaluating {name}: {e}")
        
        return results
    
    def predict(self, features):
        """Make ensemble prediction"""
        if not self.is_trained:
            logger.error("Models not trained yet!")
            return None
        
        try:
            # Scale features
            features_scaled = self.scalers['standard'].transform(features.reshape(1, -1))
            
            predictions = {}
            weights = {
                'xgboost': 0.3,
                'lightgbm': 0.3,
                'random_forest': 0.2,
                'gradient_boosting': 0.2
            }
            
            ensemble_pred = 0
            total_weight = 0
            
            for name, model in self.models.items():
                if name in weights:
                    try:
                        pred = model.predict(features_scaled)[0]
                        predictions[name] = pred
                        ensemble_pred += pred * weights[name]
                        total_weight += weights[name]
                    except Exception as e:
                        logger.error(f"Error predicting with {name}: {e}")
            
            if total_weight > 0:
                ensemble_pred /= total_weight
            
            return {
                'ensemble_prediction': ensemble_pred,
                'individual_predictions': predictions,
                'confidence': self.calculate_confidence(predictions)
            }
            
        except Exception as e:
            logger.error(f"Error making prediction: {e}")
            return None
    
    def calculate_confidence(self, predictions):
        """Calculate prediction confidence based on model agreement"""
        if len(predictions) < 2:
            return 0.5
        
        pred_values = list(predictions.values())
        std_dev = np.std(pred_values)
        mean_pred = np.mean(pred_values)
        
        # Normalize confidence (lower std_dev = higher confidence)
        confidence = max(0.1, min(0.99, 1 - (std_dev / abs(mean_pred)) if mean_pred != 0 else 0.5))
        
        return confidence
    
    def save_models(self, filepath):
        """Save trained models"""
        model_data = {
            'models': self.models,
            'scalers': self.scalers,
            'feature_columns': self.feature_columns,
            'is_trained': self.is_trained
        }
        joblib.dump(model_data, filepath)
        logger.info(f"Models saved to {filepath}")
    
    def load_models(self, filepath):
        """Load trained models"""
        try:
            model_data = joblib.load(filepath)
            self.models = model_data['models']
            self.scalers = model_data['scalers']
            self.feature_columns = model_data['feature_columns']
            self.is_trained = model_data['is_trained']
            logger.info(f"Models loaded from {filepath}")
        except Exception as e:
            logger.error(f"Error loading models: {e}")
