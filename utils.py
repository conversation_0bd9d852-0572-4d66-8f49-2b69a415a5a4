import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
import json
import os
from typing import Dict, List, Optional, Union

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataUtils:
    """Utility functions for data processing"""
    
    @staticmethod
    def clean_ohlcv_data(df: pd.DataFrame) -> pd.DataFrame:
        """Clean OHLCV data by removing invalid values"""
        try:
            # Remove rows with zero or negative prices
            df = df[(df['open'] > 0) & (df['high'] > 0) & (df['low'] > 0) & (df['close'] > 0)]
            
            # Remove rows where high < low (invalid data)
            df = df[df['high'] >= df['low']]
            
            # Remove rows where high/low are not consistent with open/close
            df = df[(df['high'] >= df['open']) & (df['high'] >= df['close'])]
            df = df[(df['low'] <= df['open']) & (df['low'] <= df['close'])]
            
            # Remove extreme outliers (price changes > 50%)
            df['price_change'] = df['close'].pct_change()
            df = df[abs(df['price_change']) <= 0.5]
            df.drop('price_change', axis=1, inplace=True)
            
            # Forward fill missing values
            df = df.fillna(method='ffill')
            
            return df
            
        except Exception as e:
            logger.error(f"Error cleaning OHLCV data: {e}")
            return df
    
    @staticmethod
    def resample_data(df: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """Resample OHLCV data to different timeframe"""
        try:
            resampled = df.resample(timeframe).agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum'
            }).dropna()
            
            return resampled
            
        except Exception as e:
            logger.error(f"Error resampling data: {e}")
            return df
    
    @staticmethod
    def calculate_returns(prices: pd.Series, method: str = 'simple') -> pd.Series:
        """Calculate returns from price series"""
        try:
            if method == 'simple':
                return prices.pct_change()
            elif method == 'log':
                return np.log(prices / prices.shift(1))
            else:
                raise ValueError("Method must be 'simple' or 'log'")
                
        except Exception as e:
            logger.error(f"Error calculating returns: {e}")
            return pd.Series()

class TechnicalIndicators:
    """Technical analysis indicators"""
    
    @staticmethod
    def sma(prices: pd.Series, period: int) -> pd.Series:
        """Simple Moving Average"""
        return prices.rolling(window=period).mean()
    
    @staticmethod
    def ema(prices: pd.Series, period: int) -> pd.Series:
        """Exponential Moving Average"""
        return prices.ewm(span=period).mean()
    
    @staticmethod
    def rsi(prices: pd.Series, period: int = 14) -> pd.Series:
        """Relative Strength Index"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        except Exception as e:
            logger.error(f"Error calculating RSI: {e}")
            return pd.Series()
    
    @staticmethod
    def macd(prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, pd.Series]:
        """MACD indicator"""
        try:
            ema_fast = TechnicalIndicators.ema(prices, fast)
            ema_slow = TechnicalIndicators.ema(prices, slow)
            macd_line = ema_fast - ema_slow
            signal_line = TechnicalIndicators.ema(macd_line, signal)
            histogram = macd_line - signal_line
            
            return {
                'macd': macd_line,
                'signal': signal_line,
                'histogram': histogram
            }
        except Exception as e:
            logger.error(f"Error calculating MACD: {e}")
            return {}
    
    @staticmethod
    def bollinger_bands(prices: pd.Series, period: int = 20, std_dev: float = 2) -> Dict[str, pd.Series]:
        """Bollinger Bands"""
        try:
            sma = TechnicalIndicators.sma(prices, period)
            std = prices.rolling(window=period).std()
            
            upper_band = sma + (std * std_dev)
            lower_band = sma - (std * std_dev)
            
            return {
                'upper': upper_band,
                'middle': sma,
                'lower': lower_band
            }
        except Exception as e:
            logger.error(f"Error calculating Bollinger Bands: {e}")
            return {}
    
    @staticmethod
    def stochastic(high: pd.Series, low: pd.Series, close: pd.Series, k_period: int = 14, d_period: int = 3) -> Dict[str, pd.Series]:
        """Stochastic Oscillator"""
        try:
            lowest_low = low.rolling(window=k_period).min()
            highest_high = high.rolling(window=k_period).max()
            
            k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
            d_percent = k_percent.rolling(window=d_period).mean()
            
            return {
                'k': k_percent,
                'd': d_percent
            }
        except Exception as e:
            logger.error(f"Error calculating Stochastic: {e}")
            return {}

class NotificationManager:
    """Handle various notification methods"""
    
    def __init__(self, config):
        self.config = config
    
    def send_telegram_message(self, message: str) -> bool:
        """Send message via Telegram bot"""
        try:
            if not self.config.TELEGRAM_BOT_TOKEN or not self.config.TELEGRAM_CHAT_ID:
                return False
            
            import requests
            
            url = f"https://api.telegram.org/bot{self.config.TELEGRAM_BOT_TOKEN}/sendMessage"
            data = {
                'chat_id': self.config.TELEGRAM_CHAT_ID,
                'text': message,
                'parse_mode': 'Markdown'
            }
            
            response = requests.post(url, data=data)
            return response.status_code == 200
            
        except Exception as e:
            logger.error(f"Error sending Telegram message: {e}")
            return False
    
    def send_discord_webhook(self, message: str) -> bool:
        """Send message via Discord webhook"""
        try:
            if not self.config.DISCORD_WEBHOOK_URL:
                return False
            
            import requests
            
            data = {'content': message}
            response = requests.post(self.config.DISCORD_WEBHOOK_URL, json=data)
            return response.status_code == 204
            
        except Exception as e:
            logger.error(f"Error sending Discord message: {e}")
            return False
    
    def send_email(self, subject: str, message: str, to_email: str) -> bool:
        """Send email notification"""
        try:
            import smtplib
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart
            
            # This would require email configuration in config.py
            # Placeholder implementation
            logger.info(f"Email notification: {subject}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending email: {e}")
            return False

class FileManager:
    """File and directory management utilities"""
    
    @staticmethod
    def ensure_directory(path: str) -> bool:
        """Ensure directory exists"""
        try:
            os.makedirs(path, exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"Error creating directory {path}: {e}")
            return False
    
    @staticmethod
    def save_json(data: dict, filepath: str) -> bool:
        """Save data to JSON file"""
        try:
            # Ensure directory exists
            directory = os.path.dirname(filepath)
            if directory:
                FileManager.ensure_directory(directory)
            
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2, default=str)
            
            return True
            
        except Exception as e:
            logger.error(f"Error saving JSON to {filepath}: {e}")
            return False
    
    @staticmethod
    def load_json(filepath: str) -> Optional[dict]:
        """Load data from JSON file"""
        try:
            if not os.path.exists(filepath):
                return None
            
            with open(filepath, 'r') as f:
                return json.load(f)
                
        except Exception as e:
            logger.error(f"Error loading JSON from {filepath}: {e}")
            return None
    
    @staticmethod
    def save_csv(df: pd.DataFrame, filepath: str) -> bool:
        """Save DataFrame to CSV"""
        try:
            directory = os.path.dirname(filepath)
            if directory:
                FileManager.ensure_directory(directory)
            
            df.to_csv(filepath, index=True)
            return True
            
        except Exception as e:
            logger.error(f"Error saving CSV to {filepath}: {e}")
            return False

class PerformanceMetrics:
    """Calculate trading performance metrics"""
    
    @staticmethod
    def calculate_sharpe_ratio(returns: pd.Series, risk_free_rate: float = 0.0) -> float:
        """Calculate Sharpe ratio"""
        try:
            excess_returns = returns - risk_free_rate
            return excess_returns.mean() / excess_returns.std() if excess_returns.std() > 0 else 0
        except Exception as e:
            logger.error(f"Error calculating Sharpe ratio: {e}")
            return 0
    
    @staticmethod
    def calculate_max_drawdown(equity_curve: pd.Series) -> float:
        """Calculate maximum drawdown"""
        try:
            peak = equity_curve.expanding().max()
            drawdown = (equity_curve - peak) / peak
            return drawdown.min()
        except Exception as e:
            logger.error(f"Error calculating max drawdown: {e}")
            return 0
    
    @staticmethod
    def calculate_calmar_ratio(returns: pd.Series, equity_curve: pd.Series) -> float:
        """Calculate Calmar ratio"""
        try:
            annual_return = returns.mean() * 252  # Assuming daily returns
            max_dd = abs(PerformanceMetrics.calculate_max_drawdown(equity_curve))
            return annual_return / max_dd if max_dd > 0 else 0
        except Exception as e:
            logger.error(f"Error calculating Calmar ratio: {e}")
            return 0
    
    @staticmethod
    def calculate_sortino_ratio(returns: pd.Series, risk_free_rate: float = 0.0) -> float:
        """Calculate Sortino ratio"""
        try:
            excess_returns = returns - risk_free_rate
            downside_returns = excess_returns[excess_returns < 0]
            downside_std = downside_returns.std() if len(downside_returns) > 0 else 0
            return excess_returns.mean() / downside_std if downside_std > 0 else 0
        except Exception as e:
            logger.error(f"Error calculating Sortino ratio: {e}")
            return 0

class ValidationUtils:
    """Data validation utilities"""
    
    @staticmethod
    def validate_ohlcv(df: pd.DataFrame) -> bool:
        """Validate OHLCV data structure"""
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        
        if not all(col in df.columns for col in required_columns):
            return False
        
        if df.empty:
            return False
        
        # Check for negative prices
        if (df[['open', 'high', 'low', 'close']] <= 0).any().any():
            return False
        
        # Check high >= low
        if (df['high'] < df['low']).any():
            return False
        
        return True
    
    @staticmethod
    def validate_signal(signal: dict) -> bool:
        """Validate trading signal structure"""
        required_fields = ['symbol', 'signal_type', 'current_price', 'confidence']
        
        if not all(field in signal for field in required_fields):
            return False
        
        if signal['confidence'] < 0 or signal['confidence'] > 1:
            return False
        
        if signal['current_price'] <= 0:
            return False
        
        valid_signal_types = ['BUY', 'SELL', 'STRONG_BUY', 'STRONG_SELL', 'HOLD']
        if signal['signal_type'] not in valid_signal_types:
            return False
        
        return True

def setup_logging(log_level: str = 'INFO', log_file: str = None):
    """Setup logging configuration"""
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    if log_file:
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format=log_format,
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
    else:
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format=log_format
        )

def format_currency(amount: float, currency: str = 'USD') -> str:
    """Format currency amount"""
    if currency == 'USD':
        return f"${amount:,.2f}"
    else:
        return f"{amount:,.4f} {currency}"

def format_percentage(value: float) -> str:
    """Format percentage value"""
    return f"{value:.2f}%"

def get_timestamp() -> str:
    """Get current timestamp string"""
    return datetime.now().strftime('%Y-%m-%d %H:%M:%S')

def calculate_position_size_kelly(win_rate: float, avg_win: float, avg_loss: float, capital: float) -> float:
    """Calculate position size using Kelly Criterion"""
    try:
        if avg_loss == 0:
            return 0
        
        win_loss_ratio = avg_win / abs(avg_loss)
        kelly_fraction = (win_rate * win_loss_ratio - (1 - win_rate)) / win_loss_ratio
        
        # Cap at 25% of capital for safety
        kelly_fraction = max(0, min(kelly_fraction, 0.25))
        
        return capital * kelly_fraction
        
    except Exception as e:
        logger.error(f"Error calculating Kelly position size: {e}")
        return 0
