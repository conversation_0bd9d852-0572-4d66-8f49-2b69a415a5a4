import asyncio
import threading
import time
import schedule
import logging
import numpy as np
from datetime import datetime, timedelta
from config import Config
from data_collector import CryptoDataCollector
from ai_predictor import AIPredictor
from signal_generator import SignalGenerator
from risk_manager import RiskManager
import json
import os

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CryptoAITradingSystem:
    def __init__(self):
        self.config = Config()
        self.data_collector = CryptoDataCollector()
        self.ai_predictor = AIPredictor()
        self.signal_generator = SignalGenerator()
        self.risk_manager = RiskManager()
        
        self.is_running = False
        self.last_training_time = None
        self.system_stats = {
            'start_time': datetime.now(),
            'total_signals_generated': 0,
            'total_trades_executed': 0,
            'system_uptime': 0,
            'last_update': datetime.now()
        }
        
    def initialize_system(self):
        """Initialize the trading system"""
        logger.info("🚀 Initializing AI Crypto Trading System...")
        
        try:
            # Start data collection
            logger.info("📊 Starting data collection...")
            self.data_collector.start_data_collection()
            
            # Train AI models if not already trained
            if not self.ai_predictor.is_trained:
                logger.info("🧠 Training AI models...")
                self.train_models()
            
            # Schedule periodic tasks
            self.schedule_tasks()
            
            logger.info("✅ System initialization complete!")
            return True
            
        except Exception as e:
            logger.error(f"❌ System initialization failed: {e}")
            return False
    
    def train_models(self):
        """Train AI prediction models"""
        try:
            logger.info("🔄 Collecting training data...")
            
            # Collect historical data for all trading pairs
            training_data = {}
            for symbol in self.config.TRADING_PAIRS[:3]:  # Start with top 3 pairs
                logger.info(f"📈 Fetching data for {symbol}...")
                df = self.data_collector.get_historical_data(symbol, '1h', 2000)
                if df is not None and len(df) > 500:
                    training_data[symbol] = df
                time.sleep(1)  # Rate limiting
            
            if not training_data:
                logger.error("❌ No training data available")
                return False
            
            # Combine data from all symbols for training
            combined_data = []
            for symbol, df in training_data.items():
                X, y, features_df = self.ai_predictor.prepare_data(df)
                if len(X) > 0:
                    combined_data.append((X, y))
            
            if not combined_data:
                logger.error("❌ No valid training data after preprocessing")
                return False
            
            # Combine all data
            all_X = np.vstack([data[0] for data in combined_data])
            all_y = np.hstack([data[1] for data in combined_data])
            
            logger.info(f"🎯 Training with {len(all_X)} samples...")
            
            # Train ensemble models
            self.ai_predictor.train_ensemble_models(all_X, all_y)
            
            # Save trained models
            self.ai_predictor.save_models('models/crypto_ai_models.pkl')
            
            self.last_training_time = datetime.now()
            logger.info("✅ Model training completed successfully!")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Model training failed: {e}")
            return False
    
    def generate_and_process_signals(self):
        """Generate trading signals and process them"""
        try:
            logger.info("🔍 Generating trading signals...")
            
            # Generate signals for all pairs
            signals = self.signal_generator.generate_signals_for_all_pairs()
            
            if not signals:
                logger.info("ℹ️ No signals generated")
                return
            
            self.system_stats['total_signals_generated'] += len(signals)
            
            # Get signal summary
            summary = self.signal_generator.get_signal_summary(signals)
            logger.info(f"📊 Signal Summary: {summary['total_signals']} total, "
                       f"{summary['buy_signals']} buy, {summary['sell_signals']} sell, "
                       f"avg confidence: {summary['avg_confidence']:.1%}")
            
            # Process top signals for trading
            top_signals = signals[:5]  # Process top 5 signals
            
            for signal in top_signals:
                # Create trade order
                trade_order = self.risk_manager.create_trade_order(signal)
                
                if trade_order:
                    logger.info(f"💼 Trade order created: {signal['symbol']} {signal['signal_type']}")
                    
                    # In a real system, you would execute the trade here
                    # For demo purposes, we'll just simulate
                    if self.simulate_trade_execution(trade_order):
                        self.system_stats['total_trades_executed'] += 1
                        logger.info(f"✅ Trade executed: {trade_order['trade_id']}")
                    
                    # Send notifications
                    self.send_signal_notification(signal)
            
            # Update system stats
            self.system_stats['last_update'] = datetime.now()
            
        except Exception as e:
            logger.error(f"❌ Error in signal generation and processing: {e}")
    
    def simulate_trade_execution(self, trade_order):
        """Simulate trade execution (replace with real broker API)"""
        try:
            # In a real system, this would connect to your broker's API
            # For simulation, we'll just execute the trade in our risk manager
            success = self.risk_manager.execute_trade(trade_order)
            return success
        except Exception as e:
            logger.error(f"❌ Trade execution failed: {e}")
            return False
    
    def send_signal_notification(self, signal):
        """Send signal notifications"""
        try:
            message = self.signal_generator.format_signal_message(signal)
            
            # Log the signal
            logger.info(f"🚨 SIGNAL: {message}")
            
            # Here you would integrate with notification services:
            # - Telegram bot
            # - Discord webhook
            # - Email
            # - SMS
            
            # Example: Save to file for now
            with open('signals_log.txt', 'a') as f:
                f.write(f"{datetime.now()}: {message}\n\n")
                
        except Exception as e:
            logger.error(f"❌ Error sending notification: {e}")
    
    def update_positions(self):
        """Update open positions with current market prices"""
        try:
            if not self.risk_manager.open_positions:
                return
            
            # Get current prices for open positions
            current_prices = {}
            for symbol in self.risk_manager.open_positions.keys():
                ticker = self.data_collector.get_real_time_data(symbol)
                if ticker:
                    current_prices[symbol] = ticker['last']
            
            # Update positions
            self.risk_manager.update_positions(current_prices)
            
        except Exception as e:
            logger.error(f"❌ Error updating positions: {e}")
    
    def schedule_tasks(self):
        """Schedule periodic tasks"""
        # Generate signals every 5 minutes
        schedule.every(5).minutes.do(self.generate_and_process_signals)
        
        # Update positions every minute
        schedule.every(1).minutes.do(self.update_positions)
        
        # Retrain models daily
        schedule.every().day.at("02:00").do(self.train_models)
        
        # Reset daily P&L at midnight
        schedule.every().day.at("00:00").do(self.risk_manager.reset_daily_pnl)
        
        logger.info("⏰ Scheduled tasks configured")
    
    def run_scheduler(self):
        """Run the task scheduler"""
        while self.is_running:
            schedule.run_pending()
            time.sleep(1)
    
    def start_system(self):
        """Start the trading system"""
        if not self.initialize_system():
            logger.error("❌ Failed to initialize system")
            return False
        
        self.is_running = True
        
        # Start scheduler in separate thread
        scheduler_thread = threading.Thread(target=self.run_scheduler)
        scheduler_thread.daemon = True
        scheduler_thread.start()
        
        # Start dashboard in separate thread
        dashboard_thread = threading.Thread(target=self.start_dashboard)
        dashboard_thread.daemon = True
        dashboard_thread.start()
        
        logger.info("🎯 AI Crypto Trading System is now running!")
        logger.info(f"📊 Dashboard available at http://localhost:{self.config.DASHBOARD_PORT}")
        
        # Run initial signal generation
        self.generate_and_process_signals()
        
        return True
    
    def start_dashboard(self):
        """Start the web dashboard"""
        try:
            from dashboard import create_dashboard
            app = create_dashboard(self)
            app.run(host=self.config.DASHBOARD_HOST,
                   port=self.config.DASHBOARD_PORT,
                   debug=False)
        except Exception as e:
            logger.error(f"❌ Dashboard startup failed: {e}")
    
    def stop_system(self):
        """Stop the trading system"""
        self.is_running = False
        self.data_collector.stop_data_collection()
        logger.info("🛑 AI Crypto Trading System stopped")
    
    def get_system_status(self):
        """Get current system status"""
        uptime = datetime.now() - self.system_stats['start_time']
        self.system_stats['system_uptime'] = str(uptime).split('.')[0]
        
        portfolio_summary = self.risk_manager.get_portfolio_summary()
        
        status = {
            'system_stats': self.system_stats,
            'portfolio_summary': portfolio_summary,
            'open_positions': self.risk_manager.open_positions,
            'current_signals': self.signal_generator.get_current_signals(),
            'is_running': self.is_running,
            'last_training': self.last_training_time
        }
        
        return status

def main():
    """Main function to run the trading system"""
    print("🚀 Starting AI Crypto Trading System...")
    print("=" * 50)
    
    # Create and start the trading system
    trading_system = CryptoAITradingSystem()
    
    try:
        if trading_system.start_system():
            print("✅ System started successfully!")
            print(f"📊 Dashboard: http://localhost:{trading_system.config.DASHBOARD_PORT}")
            print("Press Ctrl+C to stop the system")
            
            # Keep the main thread alive
            while True:
                time.sleep(1)
                
    except KeyboardInterrupt:
        print("\n🛑 Shutting down system...")
        trading_system.stop_system()
        print("👋 Goodbye!")
        
    except Exception as e:
        logger.error(f"❌ System error: {e}")
        trading_system.stop_system()

if __name__ == "__main__":
    main()
