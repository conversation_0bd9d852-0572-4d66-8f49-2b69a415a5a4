#!/usr/bin/env python3
"""
Setup script for AI Crypto Trading System
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_banner():
    """Print setup banner"""
    print("=" * 60)
    print("🚀 AI CRYPTO TRADING SYSTEM SETUP")
    print("=" * 60)
    print("Setting up your high-performance crypto trading AI...")
    print()

def check_python_version():
    """Check Python version compatibility"""
    print("🐍 Checking Python version...")
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required!")
        print(f"   Current version: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version.split()[0]} detected")
    return True

def create_directories():
    """Create necessary directories"""
    print("📁 Creating directories...")
    
    directories = [
        'models',
        'data',
        'logs',
        'backtest_results',
        'templates',
        'static'
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"   ✅ Created: {directory}/")
    
    return True

def install_dependencies():
    """Install Python dependencies"""
    print("📦 Installing dependencies...")
    
    try:
        # Upgrade pip first
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'])
        
        # Install requirements
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        
        print("✅ All dependencies installed successfully!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        print("💡 Try installing manually with: pip install -r requirements.txt")
        return False

def setup_environment():
    """Setup environment configuration"""
    print("⚙️ Setting up environment...")
    
    if not os.path.exists('.env'):
        if os.path.exists('.env.example'):
            shutil.copy('.env.example', '.env')
            print("✅ Created .env file from template")
            print("⚠️  Please edit .env file with your API keys and settings")
        else:
            print("❌ .env.example file not found")
            return False
    else:
        print("✅ .env file already exists")
    
    return True

def test_imports():
    """Test if all required modules can be imported"""
    print("🧪 Testing imports...")
    
    required_modules = [
        'numpy',
        'pandas',
        'sklearn',
        'tensorflow',
        'xgboost',
        'lightgbm',
        'ccxt',
        'flask',
        'plotly',
        'talib'
    ]
    
    failed_imports = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"   ✅ {module}")
        except ImportError:
            print(f"   ❌ {module}")
            failed_imports.append(module)
    
    if failed_imports:
        print(f"\n❌ Failed to import: {', '.join(failed_imports)}")
        print("💡 Try installing missing packages manually")
        return False
    
    print("✅ All imports successful!")
    return True

def create_sample_config():
    """Create sample configuration files"""
    print("📝 Creating sample configuration...")
    
    # Create a simple test script
    test_script = '''#!/usr/bin/env python3
"""
Quick test script for AI Crypto Trading System
"""

from config import Config
from data_collector import CryptoDataCollector
import logging

logging.basicConfig(level=logging.INFO)

def test_system():
    """Test basic system functionality"""
    print("🧪 Testing AI Crypto Trading System...")
    
    try:
        # Test configuration
        config = Config()
        print("✅ Configuration loaded")
        
        # Test data collector
        data_collector = CryptoDataCollector()
        print("✅ Data collector initialized")
        
        # Test data fetching (without API keys)
        print("📊 Testing data fetching...")
        df = data_collector.get_historical_data('BTC/USDT', '1h', 10)
        
        if df is not None and len(df) > 0:
            print(f"✅ Successfully fetched {len(df)} data points for BTC/USDT")
            print(f"   Latest price: ${df['close'].iloc[-1]:.2f}")
        else:
            print("⚠️  No data fetched (this is normal without API keys)")
        
        print("\\n🎉 Basic system test completed!")
        print("💡 Configure your API keys in .env file for full functionality")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    test_system()
'''
    
    with open('test_system.py', 'w') as f:
        f.write(test_script)
    
    print("✅ Created test_system.py")
    return True

def print_next_steps():
    """Print next steps for user"""
    print("\n" + "=" * 60)
    print("🎉 SETUP COMPLETED SUCCESSFULLY!")
    print("=" * 60)
    
    print("\n📋 NEXT STEPS:")
    print("1. Edit .env file with your API keys:")
    print("   - Binance API keys (for trading)")
    print("   - Telegram bot token (for notifications)")
    print("   - Other optional API keys")
    
    print("\n2. Test the system:")
    print("   python test_system.py")
    
    print("\n3. Run the AI trading system:")
    print("   python main.py")
    
    print("\n4. Access the dashboard:")
    print("   http://localhost:8080")
    
    print("\n5. Run backtesting:")
    print("   python backtester.py")
    
    print("\n⚠️  IMPORTANT NOTES:")
    print("- Start with paper trading to test the system")
    print("- Never risk more than you can afford to lose")
    print("- The system is for educational purposes")
    print("- Past performance doesn't guarantee future results")
    
    print("\n📚 DOCUMENTATION:")
    print("- Check README.md for detailed instructions")
    print("- Review config.py for all settings")
    print("- Monitor logs/ directory for system logs")
    
    print("\n🆘 SUPPORT:")
    print("- Check the code comments for explanations")
    print("- Review the example configurations")
    print("- Test with small amounts first")
    
    print("\n" + "=" * 60)

def main():
    """Main setup function"""
    print_banner()
    
    # Check requirements
    if not check_python_version():
        sys.exit(1)
    
    # Setup steps
    steps = [
        ("Creating directories", create_directories),
        ("Installing dependencies", install_dependencies),
        ("Setting up environment", setup_environment),
        ("Testing imports", test_imports),
        ("Creating sample files", create_sample_config)
    ]
    
    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        if not step_func():
            print(f"❌ Setup failed at: {step_name}")
            sys.exit(1)
    
    print_next_steps()

if __name__ == "__main__":
    main()
